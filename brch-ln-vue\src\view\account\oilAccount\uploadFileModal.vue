<template>
    <div class="cl-theme cl-text-list">
        <Modal v-model="show" :title="title" width="90%" :styles="{top: '20px'}">
            <div>
                <cl-table ref="attachList" :searchable="false" :columns="columns" url="/common/attachments/list"
                          :query-params="param" disable-query-on-mounted method="post" :loading="loading">
                    <div slot="buttons">
                        <Button type="primary" class="function" @click="add" v-if="showadd">添加附件</Button>
                    </div>
                </cl-table>
                <Modal ref="attach" v-model="display" @on-ok="upload" loading>
                    <cl-form v-model="attach.fileForm" :layout="attach.formLayout"></cl-form>
                </Modal>
                <div style="text-align: center">
                    <Spin size="large" fix v-if="loading"></Spin>
                    <img :src="imgUrl" style="width: 100%">
                </div>
            </div>
            <div slot="footer">
                <Button type="default" @click="cancle()">关闭</Button>
            </div>
        </Modal>
    </div>
</template>

<script>
    import axios from '@/libs/api.request'

    export default {
        name: "uploadFileModal",
        components: {},
        props: {
            format: {
                type: Array,
                default: () => {
                    return ['xls', 'xlsx', 'png', 'jpg', 'doc', 'docx', 'mp4', 'gif', 'rar', 'zip','pdf']
                }
            }
        },
        data() {
            return {
                imgUrl: null,
                screenWidth: 800,
                show: false,
                showadd: true,
                display: false,
                loading: false,
                title: '上传图片(点击图片名字查看附件)',
                attach: {
                    fileForm: {
                        file: null
                    },
                    formLayout: [
                        {
                            label: '上传图片',
                            prop: 'file',
                            formItemType: 'file',
                            width: 300,
                            format: this.format
                        }
                    ],
                },
                columns: [
                    {
                        key: 'fileName', title: '文件名',
                        render: (h, params) => {
                            return h('a', {
                                on: {
                                    click: () => {
                                        this.showPic(params.row)
                                    }
                                }
                            }, params.row.fileName)
                        }
                    },
                    {key: 'creatorName', title: '上传人'},
                    {key: 'createTime', title: '上传日期'},
                    {key: 'fileSize', title: '文件大小(KB)'},
                    {
                        title: '操作',
                        key: 'action',
                        width: 200,
                        align: 'center',
                        fixed: 'right',
                        render: (h, params) => {
                            let down =
                                h('Button', {
                                    props: {
                                        type: 'primary',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '3px'
                                    },
                                    on: {
                                        click: () => {
                                            this.download(params.row)
                                        }
                                    }
                                }, '下载')
                            let remove = h('Button', {
                                    props: {
                                        type: 'error',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.remove(params.row)
                                        }
                                    }
                                }
                                , '删除')
                            let action = [down]
                            if (!this.downloadOnly) {
                                action.push(remove)
                            }
                            return h('div', action)
                        }
                    }
                ],
                param: {
                    busiId: null,
                    busiAlias: "附件(台账)",
                    categoryCode: "file",
                    areaCode: "sc"
                }
            }
        },
        methods: {
            choose(pcid) {
                this.imgUrl = null;
                this.show = true
                this.param.busiId = pcid + "";
                this.$refs.attachList.query();
                let that = this;
                setTimeout(function () {
                    if (that.$refs.attachList.insideData && that.$refs.attachList.insideData.length > 0)
                        that.showPic(that.$refs.attachList.insideData[0])
                }, 200);
            },
            cancle() {
                this.show = false
            }, add() {
                this.display = true
                this.attach.fileForm = {}
            },
            remove(row) {
                this.$Modal.confirm({
                    title: "确认删除",
                    content: "<p>确定删除吗？</p>",
                    onOk: () => {
                        axios.request({
                            url: '/common/attachments/remove',
                            method: 'post',
                            params: {ids: row.id}
                        }).then(() => {
                            this.$refs.attachList.query()
                        })
                    },
                    onCancel: () => {
                    }
                })
            },
            reload() {
                this.$refs.attachList.query()
            },
            upload() {
                if (!this.attach.fileForm.file) {
                    this.$Message.info({content: '请选择要上传的文件！'})
                    this.$refs.attach.buttonLoading = false
                    return
                }
                axios.request({
                    url: '/common/attachments/uploadMultiFile',
                    method: 'post',
                    data: Object.assign({}, this.param, this.attach.fileForm)
                }).then(() => {
                    this.$refs.attach.buttonLoading = false
                    this.display = false
                    this.$refs.attachList.query()
                })
            },
            download(row) {
                axios.file({
                    url: '/common/attachments/download',
                    method: 'get',
                    params: {
                        id: row.id,
                        shardKey: row.shardKey,
                        thumbnail: ''
                    }
                }).then((res) => {
                    const content = res
                    const blob = new Blob([content])
                    const fileName = row.fileName
                    if ('download' in document.createElement('a')) { // 非IE下载
                        const elink = document.createElement('a')
                        elink.download = fileName
                        elink.style.display = 'none'
                        elink.href = URL.createObjectURL(blob)
                        document.body.appendChild(elink)
                        elink.click()
                        URL.revokeObjectURL(elink.href) // 释放URL 对象
                        document.body.removeChild(elink)
                    } else { // IE10+下载
                        navigator.msSaveBlob(blob, fileName)
                    }
                })
            }, showPic(row) {
                if (row.litimgUrl) {
                    this.imgUrl = row.litimgUrl;
                }
                // 兼容minio迁移，迁移完成后，后面的else可以删除
                else if (row.objectName){
                  const baseUrl = window.location.origin;
                  axios.request({
                    url: '/file/getPreviewUrl/' + row.bucketName,
                    method: 'get',
                    params: {
                      url: baseUrl + row.url
                    }
                  }).then((res) => {
                    row.litimgUrl = res.data.url;
                    this.imgUrl = res.data.url;
                    let bgImg = new Image()
                    bgImg.src = this.imgUrl // 获取背景图片的url
                    bgImg.onerror = () => {
                      row.litimgUrl = null;
                      this.$Message.error("图片加载失败！！！");
                      this.loading = false;
                    }
                    bgImg.onload = () => { // 等背景图片加载成功后 去除loading
                      this.loading = false;
                    }
                  })
                }
                else {
                    let sub = row.fileName.split(".");
                    let key = row.mongodbFileId + "." + sub[sub.length - 1];
                    this.loading = true;
                    axios.request({
                        url: '/common/attachments/share/' + key,
                        method: 'get',
                    }).then((res) => {
                        row.litimgUrl = res.data.url;
                        this.imgUrl = res.data.url;
                        let bgImg = new Image()
                        bgImg.src = this.imgUrl // 获取背景图片的url
                        bgImg.onerror = () => {
                            row.litimgUrl = null;
                            this.$Message.error("图片加载失败！！！");
                            this.loading = false;
                        }
                        bgImg.onload = () => { // 等背景图片加载成功后 去除loading
                            this.loading = false;
                        }
                        // this.imgUrl = "http://objects.objs.paas.sc.ctc.com/s/28aa0721445452647828ccd2e3a0602a.jpg?token=65794a68624763694f694a49557a49314e694973496e523563434936496b705856434a392e65794a6964574e725a5851694f694a7a5932356f4c575a70624755694c434a6c654841694f6a45314e6a67334d4445334e6a5173496d3969616d566a64434936496a4934595745774e7a49784e4451314e4455794e6a51334f44493459324e6b4d6d557a595441324d444a684c6d70775a794a392e625765664946333557794e723236554d3239394b314b6c634f6763646b516f54356f72557238484f794e6b";
                    })
                }
            }
        }, mounted() {
            const that = this;
            window.onresize = () => {
                return (() => {
                    window.screenWidth = document.body.clientWidth;
                    that.screenWidth = window.screenWidth * 0.7;
                })();
            };
            window.onresize();
        }

    }
</script>

<style scoped></style>
