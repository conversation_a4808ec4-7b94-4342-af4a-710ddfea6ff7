<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sccl.modules.business.poweraudit.mapper.PowerAuditMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sccl.modules.business.poweraudit.entity.PowerAuditEntity">
        <result column="id" property="id" />
        <result column="city" property="city" />
        <result column="city_code" property="cityCode" />
        <result column="month" property="month" />
        <result column="county_companies" property="countyCompanies" />
        <result column="county_companies_code" property="countyCompaniesCode" />
        <result column="operations_branch" property="operationsBranch" />
        <result column="ammeterid" property="ammeterid" />
        <result column="stationcode" property="stationcode" />
        <result column="tower_site_code" property="towerSiteCode" />
        <result column="mss_account_id" property="mssAccountId" />
        <result column="apply_time" property="applyTime" />
        <result column="muti_jtlte_codes" property="mutiJtlteCodes" />
        <result column="address_consistence" property="addressConsistence" />
        <result column="payment_consistence" property="paymentConsistence" />
        <result column="repeat" property="repeat" />
        <result column="electricity_rationality" property="electricityRationality" />
        <result column="electricity_continuity" property="electricityContinuity" />
        <result column="electricity_meter" property="electricityMeter" />
        <result column="periodic_anomaly" property="periodicAnomaly" />
        <result column="exclusive_accuracy" property="exclusiveAccuracy" />
        <result column="share_accuracy" property="shareAccuracy" />
        <result column="consume_continuity" property="consumeContinuity" />
        <result column="fluctuate_continuity" property="fluctuateContinuity" />
        <result column="electricity_prices" property="electricityPrices" />
        <result column="reimbursement_cycle" property="reimbursementCycle" />
        <result column="if_qk_success" property="ifQkSuccess" />
        <result column="if_success" property="ifSuccess" />
        <result column="site_type" property="siteType" />
        <result column="audit_time" property="auditTime" />
        <result column="pcid" property="pcid" />
        <result column="operations_branch" property="operationsBranch" />
        <result column="ledger_period" property="ledgerPeriod" />
        <result column="consistency_proportion" property="consistencyProportion" />
        <result column="pam_id" property="pamId" />
    </resultMap>

    <resultMap id="DetailsMap" type="com.sccl.modules.business.poweraudit.entity.DetailsDTO" extends="BaseResultMap">

    </resultMap>


    <insert id="insertPowerAudit" parameterType="com.sccl.modules.business.poweraudit.entity.PowerAuditEntity">
        insert into power_audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,city,county_companies,`month`,pcid,ammeterid,stationcode,tower_site_code,mss_account_id,apply_time,muti_jtlte_codes,address_consistence,payment_consistence,
            `repeat`,electricity_rationality,electricity_continuity,electricity_meter,periodic_anomaly,exclusive_accuracy,share_accuracy,reimbursement_cycle,electricity_prices,
            consume_continuity,fluctuate_continuity,if_qk_success,if_success,site_type,audit_time,operations_branch,ledger_period,city_code,county_companies_code,pam_id
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id}, #{city}, #{countyCompanies}, #{month}, #{pcid}, #{ammeterid}, #{stationcode}, #{towerSiteCode}, #{mssAccountId}, #{applyTime},
            #{mutiJtlteCodes}, #{addressConsistence}, #{paymentConsistence}, #{repeat}, #{electricityRationality}, #{electricityContinuity},
            #{electricityMeter}, #{periodicAnomaly}, #{exclusiveAccuracy}, #{shareAccuracy}, #{reimbursementCycle}, #{electricityPrices}, #{consumeContinuity},
            #{fluctuateContinuity}, #{ifQkSuccess},#{ifSuccess}, #{siteType}, #{auditTime},
            #{operationsBranch},#{ledgerPeriod},#{cityCode},#{countyCompaniesCode},#{pamId}
        </trim>
    </insert>

    <!-- 批量插入 -->
    <insert id="insertList" parameterType="java.util.List" useGeneratedKeys="false">
        insert into power_audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            city,
            county_companies,
            `month`,
            pcid,
            ammeterid,
            stationcode,
            tower_site_code,
            mss_account_id,
            apply_time,
            muti_jtlte_codes,
            address_consistence,
            payment_consistence,
            `repeat`,
            electricity_rationality,
            electricity_continuity,
            electricity_meter,
            periodic_anomaly,
            exclusive_accuracy,
            share_accuracy,
            reimbursement_cycle,
            electricity_prices,
            consume_continuity,
            fluctuate_continuity,
            if_qk_success,
            if_success,
            site_type,
            audit_time,
            operations_branch,
            ledger_period,
            city_code,
            county_companies_code,
            consistency_proportion,
            pam_id,
        </trim>
        values
        <foreach collection="powerAuditEntities" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.id},
                #{item.city},
                #{item.countyCompanies},
                #{item.month},
                #{item.pcid},
                #{item.ammeterid},
                #{item.stationcode},
                #{item.towerSiteCode},
                #{item.mssAccountId},
                #{item.applyTime},
                #{item.mutiJtlteCodes},
                #{item.addressConsistence},
                #{item.paymentConsistence},
                #{item.repeat},
                #{item.electricityRationality},
                #{item.electricityContinuity},
                #{item.electricityMeter},
                #{item.periodicAnomaly},
                #{item.exclusiveAccuracy},
                #{item.shareAccuracy},
                #{item.reimbursementCycle},
                #{item.electricityPrices},
                #{item.consumeContinuity},
                #{item.fluctuateContinuity},
                #{item.ifQkSuccess},
                #{item.ifSuccess},
                #{item.siteType},
                #{item.auditTime},
                #{item.operationsBranch},
                #{item.ledgerPeriod},
                #{item.city},
                #{item.countyCompaniesCode},
                #{item.consistencyProportion},
                #{item.pamId},
            </trim>
        </foreach>
    </insert>

    <update id="updateByPrimaryKey">
        update power_audit
        <trim prefix="SET" suffixOverrides=",">
            city = #{city},
            county_companies = #{countyCompanies},
            `month` = #{month},
            pcid = #{pcid},
            ammeterid = #{ammeterid},
            stationcode = #{stationcode},
            tower_site_code = #{towerSiteCode},
            mss_account_id = #{mssAccountId},
            apply_time = #{applyTime},
            muti_jtlte_codes = #{mutiJtlteCodes},
            address_consistence = #{addressConsistence},
            payment_consistence = #{paymentConsistence},
            repeat = #{repeat},
            electricity_rationality = #{electricityRationality},
            electricity_continuity = #{electricityContinuity},
            electricity_meter = #{electricityMeter},
            periodic_anomaly = #{periodicAnomaly},
            exclusive_accuracy = #{exclusiveAccuracy},
            share_accuracy = #{shareAccuracy},
            reimbursement_cycle = #{reimbursementCycle},
            electricity_prices = #{electricityPrices},
            consume_continuity = #{consumeContinuity},
            fluctuate_continuity = #{fluctuateContinuity},
            if_qk_success = #{ifQkSuccess}
            if_success = #{ifSuccess},
            site_type = #{siteType},
            audit_time = #{auditTime},
            consistency_proportion = #{consistencyProportion},
            pam_id = #{pamId},
        </trim>
        where id = #{id}
    </update>

    <update id="updateForModel" parameterType="com.sccl.modules.business.poweraudit.entity.PowerAuditEntity">
        update
            power_audit
        <trim prefix="SET" suffixOverrides=",">
            <if test="cityCode != null  and cityCode != ''  ">city_code = #{cityCode},</if>
            <if test="city != null  and city != ''  ">city = #{city},</if>
            <if test="countyCompanies != null  and countyCompanies != ''  ">county_companies = #{countyCompanies},</if>
            <if test="countyCompaniesCode != null  and countyCompaniesCode != ''  ">county_companies_code = #{countyCompaniesCode},</if>
            <if test="operationsBranch != null  and operationsBranch != ''  ">operations_branch = #{operationsBranch},</if>
            <if test="month != null  and month != ''  ">`month` = #{month},</if>
            <if test="pcid != null  and pcid != ''  ">pcid = #{pcid},</if>
            <if test="ammeterid != null  and ammeterid != ''  ">ammeterid = #{ammeterid},</if>
            <if test="stationcode != null  and stationcode != ''  ">stationcode = #{stationcode},</if>
            <if test="towerSiteCode != null  and towerSiteCode != ''  ">tower_site_code = #{towerSiteCode},</if>
            <if test="mssAccountId != null  and mssAccountId != ''  ">mss_account_id = #{mssAccountId},</if>
            <if test="applyTime != null  ">apply_time = #{applyTime},</if>
            <if test="mutiJtlteCodes != null  and mutiJtlteCodes != ''  ">muti_jtlte_codes = #{mutiJtlteCodes},</if>
            <if test="addressConsistence != null  and addressConsistence != ''  ">address_consistence = #{addressConsistence},</if>
            <if test="paymentConsistence != null  and paymentConsistence != ''  ">payment_consistence = #{paymentConsistence},</if>
            <if test="repeat != null  and repeat != ''  ">`repeat` = #{repeat},</if>
            <if test="electricityRationality != null  and electricityRationality != ''  ">electricity_rationality = #{electricityRationality},</if>
            <if test="electricityContinuity != null  and electricityContinuity != ''  ">electricity_continuity = #{electricityContinuity},</if>
            <if test="electricityMeter != null  and electricityMeter != ''  ">electricity_meter = #{electricityMeter},</if>
            <if test="periodicAnomaly != null  and periodicAnomaly != ''  ">periodic_anomaly = #{periodicAnomaly},</if>
            <if test="exclusiveAccuracy != null  and exclusiveAccuracy != ''  ">exclusive_accuracy = #{exclusiveAccuracy},</if>
            <if test="shareAccuracy != null  and shareAccuracy != ''  ">share_accuracy = #{shareAccuracy},</if>
            <if test="reimbursementCycle != null  and reimbursementCycle != ''  ">reimbursement_cycle = #{reimbursementCycle},</if>
            <if test="electricityPrices != null  and electricityPrices != ''  ">electricity_prices = #{electricityPrices},</if>
            <if test="consumeContinuity != null  and consumeContinuity != ''  ">consume_continuity = #{consumeContinuity},</if>
            <if test="fluctuateContinuity != null  and fluctuateContinuity != ''  ">fluctuate_continuity = #{fluctuateContinuity},</if>
            <if test="ifQkSuccess != null  ">if_qk_success = #{ifQkSuccess},</if>
            <if test="ifSuccess != null  ">if_success = #{ifSuccess},</if>
            <if test="siteType != null  ">site_type = #{siteType},</if>
            <if test="auditTime != null  ">audit_time = #{auditTime},</if>
            <if test="consistencyProportion != null  ">consistency_proportion = #{consistencyProportion},</if>
            <if test="pamId != null  ">pam_id = #{pamId},</if>
        </trim>
        where id = #{id}
    </update>
    <!-- 逻辑删除 -->
    <update id="deleteByPrimaryKey" parameterType="Map">
        UPDATE power_audit SET DEL_FLAG='1' where id = #{id}
        <if test="shardKey != null and shardKey != ''"> and shardKey = #{shardKey}</if>
    </update>

    <update id="deleteByIds" parameterType="String">
        UPDATE power_audit SET DEL_FLAG='1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 物理删除 -->
    <delete id="deleteByPrimaryKeyDB" parameterType="Map">
        delete from power_audit where id = #{id}
        <if test="shardKey != null and shardKey != ''"> and shardKey = #{shardKey}</if>
    </delete>

    <delete id="deleteByIdsDB" parameterType="String">
        delete from power_audit where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, city,city_code,county_companies,county_companies_code,operations_branch, `month`, ammeterid, stationcode, tower_site_code, mss_account_id, apply_time, muti_jtlte_codes, address_consistence, payment_consistence, `repeat`,
        electricity_rationality, electricity_continuity, electricity_meter, periodic_anomaly, exclusive_accuracy, share_accuracy, consume_continuity, fluctuate_continuity,
        if_qk_success,if_success,site_type,electricity_prices,reimbursement_cycle,audit_time,pcid,consistency_proportion,pam_id
    </sql>

    <select id="getPowerAuditByCity" resultType="com.sccl.modules.business.poweraudit.entity.PowerAuditDTO">
        SELECT
        so.org_name city,
        pu.city_code,
        pu.county_companies_code,
        so2.org_name operationsBranch,
        pu.`month`,
        COUNT( 1 ) sums,
        COUNT( CASE WHEN muti_jtlte_codes = '是' THEN 1 ELSE NULL END ) mutiJtlteCodes,
        COUNT( CASE WHEN address_consistence = '否' THEN 1 ELSE NULL END ) addressConsistence,
        COUNT( CASE WHEN payment_consistence = '否' THEN 1 ELSE NULL END ) paymentConsistence,
        COUNT( CASE WHEN `repeat` = '否' THEN 1 ELSE NULL END ) repeats,
        COUNT( CASE WHEN electricity_continuity = '否' THEN 1 ELSE NULL END ) electricityContinuity,
        COUNT( CASE WHEN electricity_rationality = '否' THEN 1 ELSE NULL END ) electricityRationality,
        COUNT( CASE WHEN fluctuate_continuity = '否' THEN 1 ELSE NULL END ) fluctuateContinuity,
        COUNT( CASE WHEN consume_continuity = '否' THEN 1 ELSE NULL END ) consumeContinuity,
        COUNT( CASE WHEN share_accuracy = '否' THEN 1 ELSE NULL END ) shareAccuracy,
        COUNT( CASE WHEN periodic_anomaly = '否' THEN 1 ELSE NULL END ) periodicAnomaly,
        COUNT( CASE WHEN electricity_meter = '是' THEN 1 ELSE NULL END ) electricityMeter,
        COUNT( CASE WHEN electricity_prices = '否' THEN 1 ELSE NULL END ) electricityPrices,
        COUNT( CASE WHEN reimbursement_cycle = '否' THEN 1 ELSE NULL END ) reimbursementCycle,
        COUNT( CASE WHEN consistency_proportion = '否' THEN 1 ELSE NULL END ) consistencyProportion,
        if_success,
        site_type
        FROM
        ecm.power_audit pu
        LEFT JOIN rmp.sys_organizations so ON pu.city_code = so.id
        LEFT JOIN rmp.sys_organizations so2 ON pu.county_companies_code = so2.id
        WHERE
        pu.if_success = 0
        AND pu.city_code IS NOT NULL
        <if test="powerAuditVO.cityCode != '' and powerAuditVO.cityCode != null">
            AND pu.city_code = #{powerAuditVO.cityCode}
        </if>
        <if test="powerAuditVO.countyCompaniesCode != '' and powerAuditVO.countyCompaniesCode != null">
            AND pu.county_companies_code = #{powerAuditVO.countyCompaniesCode}
        </if>
        <if test="powerAuditVO.siteType != '' and powerAuditVO.siteType != null">
            AND pu.site_type = #{powerAuditVO.siteType}
        </if>
        <if test="powerAuditVO.month != '' and powerAuditVO.month != null">
            AND pu.`month` = #{powerAuditVO.month}
        </if>
        <choose>
            <when test="powerAuditVO.mssAccountId != null and powerAuditVO.mssAccountId == '1'">
                AND pu.mss_account_id IS NOT NULL
            </when>
            <otherwise>
                AND pu.mss_account_id IS NULL
            </otherwise>
        </choose>
        GROUP BY
        city_code,
        county_companies_code,
        `month`,
        if_success,
        site_type
    </select>

    <select id="getPowerAuditCompanies" resultType="com.sccl.modules.business.poweraudit.entity.PowerAuditDTO">
        SELECT
        so.org_name as city,
        so1.org_name operationsBranch,
        pu.city_code,
        pu.county_companies_code,
        pu.`month`,
        COUNT( 1 ) sums,
        COUNT( CASE WHEN muti_jtlte_codes = '是' THEN 1 ELSE NULL END ) mutiJtlteCodes,
        COUNT( CASE WHEN address_consistence = '否' THEN 1 ELSE NULL END ) addressConsistence,
        COUNT( CASE WHEN payment_consistence = '否' THEN 1 ELSE NULL END ) paymentConsistence,
        COUNT( CASE WHEN `repeat` = '否' THEN 1 ELSE NULL END ) repeats,
        COUNT( CASE WHEN electricity_continuity = '否' THEN 1 ELSE NULL END ) electricityContinuity,
        COUNT( CASE WHEN electricity_rationality = '否' THEN 1 ELSE NULL END ) electricityRationality,
        COUNT( CASE WHEN fluctuate_continuity = '否' THEN 1 ELSE NULL END ) fluctuateContinuity,
        COUNT( CASE WHEN consume_continuity = '否' THEN 1 ELSE NULL END ) consumeContinuity,
        COUNT( CASE WHEN share_accuracy = '否' THEN 1 ELSE NULL END ) shareAccuracy,
        COUNT( CASE WHEN exclusive_accuracy = '否' THEN 1 ELSE NULL END ) exclusiveAccuracy,
        COUNT( CASE WHEN periodic_anomaly = '否' THEN 1 ELSE NULL END ) periodicAnomaly,
        COUNT( CASE WHEN electricity_meter = '是' THEN 1 ELSE NULL END ) electricityMeter,
        COUNT( CASE WHEN electricity_prices = '否' THEN 1 ELSE NULL END ) electricityPrices,
        COUNT( CASE WHEN reimbursement_cycle = '否' THEN 1 ELSE NULL END ) reimbursementCycle,
        COUNT( CASE WHEN consistency_proportion = '否' THEN 1 ELSE NULL END ) consistencyProportion,
        if_success,
        site_type
        FROM
        ecm.power_audit pu
        LEFT JOIN rmp.sys_organizations so ON pu.city_code = so.id
        LEFT JOIN rmp.sys_organizations so1 ON so1.id = pu.county_companies_code
        WHERE
        pu.if_success = 0
        AND pu.city_code IS NOT NULL
        <if test="powerAuditVO.cityCode != null and powerAuditVO.cityCode != ''">
            AND pu.city_code = #{powerAuditVO.cityCode}
        </if>
        <if test="powerAuditVO.countyCompaniesCode != null and powerAuditVO.countyCompaniesCode != ''">
            AND pu.county_companies_code = #{powerAuditVO.countyCompaniesCode}
        </if>
        <if test="powerAuditVO.siteType != null and powerAuditVO.siteType != ''">
            AND pu.site_type = #{powerAuditVO.siteType}
        </if>
        <if test="powerAuditVO.month != null and powerAuditVO.month != ''">
            AND pu.`month` = #{powerAuditVO.month}
        </if>
        <choose>
            <when test="powerAuditVO.mssAccountId != null and powerAuditVO.mssAccountId == '1'">
                AND pu.mss_account_id IS NOT NULL
            </when>
            <otherwise>
                AND pu.mss_account_id IS NULL
            </otherwise>
        </choose>
        GROUP BY
        city_code,
        county_companies_code,
        `month`,
        if_success,
        site_type
    </select>

    <select id="getAuditOperationsBranch" resultType="com.sccl.modules.business.poweraudit.entity.PowerAuditDTO">
        SELECT
            city,city_code cityCode,county_companies countyCompanies,county_companies_code countyCompaniesCode,
            operations_branch operationsBranch, ledger_period as `month`, COUNT(1) sums,
            COUNT(CASE WHEN muti_jtlte_codes = '是' THEN 1 ELSE NULL END) mutiJtlteCodes,
            COUNT(CASE WHEN address_consistence = '否' THEN 1 ELSE NULL END ) addressConsistence,
            COUNT(CASE WHEN payment_consistence = '否' THEN 1 ELSE NULL END ) paymentConsistence,
            COUNT(CASE WHEN `repeat` = '否' THEN 1 ELSE NULL END ) repeats,
            COUNT(CASE WHEN electricity_continuity = '否' THEN 1 ELSE NULL END ) electricityContinuity,
            COUNT(CASE WHEN electricity_rationality = '否' THEN 1 ELSE NULL END ) electricityRationality,
            COUNT(CASE WHEN fluctuate_continuity = '否' THEN 1 ELSE NULL END ) fluctuateContinuity,
            COUNT(CASE WHEN consume_continuity = '否' THEN 1 ELSE NULL END ) consumeContinuity,
            COUNT(CASE WHEN share_accuracy = '否' THEN 1 ELSE NULL END ) shareAccuracy,
            COUNT( CASE WHEN exclusive_accuracy = '否' THEN 1 ELSE NULL END ) exclusiveAccuracy,
            COUNT(CASE WHEN periodic_anomaly = '否' THEN 1 ELSE NULL END ) periodicAnomaly,
            COUNT(CASE WHEN electricity_meter = '是' THEN 1 ELSE NULL END ) electricityMeter,
            COUNT(CASE WHEN electricity_prices = '否' THEN 1 ELSE NULL END ) electricityPrices,
            COUNT(CASE WHEN reimbursement_cycle = '否' THEN 1 ELSE NULL END ) reimbursementCycle,
            COUNT(CASE WHEN consistency_proportion = '否' THEN 1 ELSE NULL END ) consistencyProportion,
            if_success,site_type
        FROM ecm.power_audit
        WHERE
            city_code IS NOT NULL
        <if test="powerAuditVO.cityCode != '' and powerAuditVO.cityCode != null">
            and city_code = #{powerAuditVO.cityCode}
        </if>
        <if test="powerAuditVO.countyCompaniesCode != '' and powerAuditVO.countyCompaniesCode != null">
            and county_companies_code = #{powerAuditVO.countyCompaniesCode}
        </if>
        <if test="powerAuditVO.siteType != '' and powerAuditVO.siteType != null">
            and site_type = #{powerAuditVO.siteType}
        </if>
        <if test="powerAuditVO.month != '' and powerAuditVO.month != null">
            and ledger_period = #{powerAuditVO.month}
        </if>
        <if test="powerAuditVO.operationsBranch != '' and powerAuditVO.operationsBranch != null">
            and operations_branch = #{powerAuditVO.operationsBranch}
        </if>
        <choose>
            <when test="powerAuditVO.mssAccountId != null and powerAuditVO.mssAccountId == '1'">
                AND mss_account_id IS NOT NULL
            </when>
            <otherwise>
                AND mss_account_id IS NULL
            </otherwise>
        </choose>
        GROUP BY `operations_branch`,ledger_period
        ORDER BY ledger_period DESC
    </select>

    <select id="getPowerAudityByPcid" resultMap="BaseResultMap">
        SELECT
            *
        FROM power_audit
        WHERE (audit_time,pcid) in (
            SELECT
                max(audit_time) as audit_time,
                pcid
            FROM power_audit
            WHERE pcid in
                <foreach collection="pcids" item="pcid" open="(" separator="," close=")">
                    #{pcid}
                </foreach>
                <choose>
                    <when test="type != null and type == '1'">
                        AND mss_account_id IS NOT NULL
                    </when>
                    <otherwise>
                        AND mss_account_id IS NULL
                    </otherwise>
                </choose>
            GROUP BY pcid)
        ORDER BY audit_time DESC
    </select>

    <select id="getAuditResult" resultMap="BaseResultMap">
        SELECT t.id, t.city,t.city_code,t.county_companies,t.county_companies_code, t.`month`, t.ammeterid, t.stationcode, t.tower_site_code, t.mss_account_id, t.apply_time,
        t.muti_jtlte_codes, t.address_consistence, t.payment_consistence, t.`repeat`,  t.electricity_rationality, t.electricity_continuity, t.electricity_meter,
        t.periodic_anomaly, t.exclusive_accuracy, t.share_accuracy, t.consume_continuity, t.fluctuate_continuity,  t.if_qk_success,t.if_success,t.site_type,
        t.electricity_prices,t.reimbursement_cycle,t.audit_time,t.pcid,t.pam_id,t.consistency_proportion
        FROM ecm.power_audit t
        WHERE ( SELECT COUNT(*) FROM ecm.power_audit WHERE ammeterid = t.ammeterid AND audit_time >= t.audit_time ) &lt;= 3
        and (t.apply_time > '2022-01-01 00:00:00' or t.apply_time is null)
        and t.pam_id in
        <foreach collection="ammeterids" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        and t.mss_account_id is not null
        ORDER BY t.ammeterid DESC, t.apply_time DESC
    </select>

    <select id="getAuditByMssAccountId"  resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ecm.power_audit
        WHERE mss_account_id = #{mssAccountId}
        ORDER BY audit_time DESC LIMIT 1
    </select>

    <select id="getAuditByPcid"  resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ecm.power_audit
        WHERE pcid = #{pcid}
            <choose>
                <when test="type != null and type == '1'">
                    AND mss_account_id IS NOT NULL
                </when>
                <otherwise>
                    AND mss_account_id IS NULL
                </otherwise>
            </choose>
        ORDER BY audit_time DESC LIMIT 1
    </select>

    <select id="getAuditByInfo"  resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ecm.power_audit
        WHERE
            pcid = #{pcid}
            <choose>
                <when test="mssAccountId != null and mssAccountId != ''">
                    AND mss_account_id = #{mssAccountId}
                </when>
                <otherwise>
                    AND mss_account_id IS NULL
                </otherwise>
            </choose>
        ORDER BY audit_time DESC
        LIMIT 1
    </select>

    <select id="getAuditDetails" parameterType="com.sccl.modules.business.poweraudit.entity.PowerAuditVO" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ecm.power_audit
        WHERE 1=1
        <if test="powerAuditVO.month != '' and powerAuditVO.month != null">
            and ledger_period = #{powerAuditVO.month}
        </if>
        <if test="powerAuditVO.cityCode != '' and powerAuditVO.cityCode != null">
            and city_code = #{powerAuditVO.cityCode}
        </if>
        <if test="powerAuditVO.operationsBranch != '' and powerAuditVO.operationsBranch != null">
            and operations_branch = #{powerAuditVO.operationsBranch}
        </if>
    </select>

    <select id="getAuditDetailsNew" parameterType="com.sccl.modules.business.poweraudit.entity.PowerAuditVO" resultMap="DetailsMap">
        SELECT DISTINCT
        pu.id,
        pu.`month` AS accountNo,
        pu.`month`,
        so.org_name city,
        pu.city_code,
        so1.org_name county_companies,
        pu.county_companies_code,
        so1.org_name AS operations_branch,
        pu.ammeterid,
        pu.stationcode,
        pu.tower_site_code,
        pu.mss_account_id,
        pu.apply_time,
        pu.muti_jtlte_codes,
        pu.address_consistence,
        pu.payment_consistence,
        pu.`repeat`,
        pu.electricity_rationality,
        pu.electricity_continuity,
        pu.electricity_meter,
        pu.periodic_anomaly,
        pu.exclusive_accuracy,
        pu.share_accuracy,
        pu.consume_continuity,
        pu.fluctuate_continuity,
        pu.if_qk_success,
        pu.if_success,
        pu.site_type,
        pu.electricity_prices,
        pu.reimbursement_cycle,
        pu.audit_time,
        pu.pcid,
        pu.consistency_proportion,
        pu.pam_id,
        pu.audit_time AS auditTimeNow,
        pa.lasteditdate
        FROM
        ecm.power_audit pu
        LEFT JOIN rmp.sys_organizations so ON pu.city_code = so.id
        LEFT JOIN rmp.sys_organizations so1 ON pu.county_companies_code = so1.id
        LEFT JOIN ecm.power_account pa ON pu.pcid = pa.pcid
        WHERE
        pu.if_success = 0
        <if test="powerAuditVO.cityCode != '' and powerAuditVO.cityCode != null">
            and pu.city_code = #{powerAuditVO.cityCode}
        </if>
        <if test="powerAuditVO.countyCompaniesCode != '' and powerAuditVO.countyCompaniesCode != null">
            and pu.county_companies_code = #{powerAuditVO.countyCompaniesCode}
        </if>
        <if test="powerAuditVO.siteType != '' and powerAuditVO.siteType != null">
            and pu.site_type = #{powerAuditVO.siteType}
        </if>
        <if test="powerAuditVO.month != '' and powerAuditVO.month != null">
            and pu.`month` = #{powerAuditVO.month}
        </if>
        <if test="powerAuditVO.mutiJtlteCodes != null and powerAuditVO.mutiJtlteCodes != ''">
            and pu.muti_jtlte_codes = #{powerAuditVO.mutiJtlteCodes}
        </if>
        <if test="powerAuditVO.addressConsistence != null and powerAuditVO.addressConsistence != ''">
            and pu.address_consistence = #{powerAuditVO.addressConsistence}
        </if>
        <if test="powerAuditVO.periodicAnomaly != null and powerAuditVO.periodicAnomaly != ''">
            and pu.periodic_anomaly = #{powerAuditVO.periodicAnomaly}
        </if>
        <if test="powerAuditVO.electricityContinuity != null and powerAuditVO.electricityContinuity != ''">
            and pu.electricity_continuity = #{powerAuditVO.electricityContinuity}
        </if>
        <if test="powerAuditVO.electricityPrices != null and powerAuditVO.electricityPrices != ''">
            and pu.electricity_prices = #{powerAuditVO.electricityPrices}
        </if>
        <if test="powerAuditVO.electricityRationality != null and powerAuditVO.electricityRationality != ''">
            and pu.electricity_rationality = #{powerAuditVO.electricityRationality}
        </if>
        <if test="powerAuditVO.fluctuateContinuity != null and powerAuditVO.fluctuateContinuity != ''">
            and pu.fluctuate_continuity = #{powerAuditVO.fluctuateContinuity}
        </if>
        <if test="powerAuditVO.shareAccuracy != null and powerAuditVO.shareAccuracy != ''">
            and pu.share_accuracy = #{powerAuditVO.shareAccuracy}
        </if>
        <if test="powerAuditVO.reimbursementCycle != null and powerAuditVO.reimbursementCycle != ''">
            and pu.reimbursement_cycle = #{powerAuditVO.reimbursementCycle}
        </if>
        <if test="powerAuditVO.consistencyProportion != null and powerAuditVO.consistencyProportion != ''">
            and pu.consistency_proportion = #{powerAuditVO.consistencyProportion}
        </if>
        <choose>
            <when test="powerAuditVO.mssAccountId != null and powerAuditVO.mssAccountId == '1'">
                AND pu.mss_account_id IS NOT NULL
            </when>
            <otherwise>
                AND pu.mss_account_id IS NULL
            </otherwise>
        </choose>
    </select>
    <select id="getAuditDetailsMore" resultMap="DetailsMap">
        SELECT
        t.pcid,
        (CASE WHEN pa.category = 1 THEN pa.ammetername ELSE pa.protocolname END ) ammeterid,
        t.toweraccountid,
        t.startdate  as startTime,
        t.enddate as stopTime,
        t.prevtotalreadings as startDegrees,
        t.curtotalreadings as stopDegrees,
        t.totalusedreadings as degrees ,
        t.inputdate as auditTimeNow ,
        t.preStartdate as lastStartTime,
        t.preEnddate as lastStopTime,
        t.prePrevtotalreadings as lastStartDegrees,
        t.preCurtotalreadings as lastStopDegrees,
        t.preTotalusedreadings as lastDegrees,
        t.preInputdate as auditTimeLast,
        CASE WHEN t.preEnddate = t.startdate THEN
        '报账时间重叠'
        ELSE
        '报账时间连续缺失'
        END  as  periodErrorType,
        CASE WHEN t.prevtotalreadings = t.prePrevtotalreadings THEN
        '报账度数重叠'
        ELSE
        '报账度数连续缺失'
        END  as degreeErrorType,
        CASE
        WHEN psi.isshare = 1 and IFNULL(LENGTH(psi.sheredepartname) - LENGTH(REPLACE(psi.sheredepartname, ',', '')) + 1,0) =1  THEN
        '共享家数=1，基础信息设置为共享'
        WHEN psi.isshare = 0 and IFNULL(LENGTH(psi.sheredepartname) - LENGTH(REPLACE(psi.sheredepartname, ',', '')) + 1,0)>1  THEN
        '共享家数>1，基础信息设置为独享'
        WHEN psi.isshare is null and psi.sharenum is null then '未设置独享共享'
        else 	'未设置独享共享'
        END  as shareErrorType,
        ifnull(ta.mobile_apportionmentratio, 0) mobileApportionmentratio,
        ifnull(ta.unicom_apportionmentratio, 0) unicomApportionmentratio,
        ifnull(ta.expand_apportionmentratio, 0) expandApportionmentratio,
        ifnull(ta.energy_apportionmentratio, 0) energyApportionmentratio,
        ifnull(ta.apportionmentratio, 0) dxApportionmentratio,
        ifnull(ta.mobile_apportionmentratio, 0)+ifnull(ta.unicom_apportionmentratio, 0)+
        ifnull(ta.expand_apportionmentratio, 0)+ifnull(ta.energy_apportionmentratio, 0)+ifnull(ta.apportionmentratio, 0)
        totalApportionmentratio,
        pa.percent as meterPercent,
        GREATEST(IFNULL(LENGTH(psi.sheredepartname) - LENGTH(REPLACE(psi.sheredepartname, ',', '')) + 1,0),IFNULL(psi.sharenum,0)) as shareNum,
        datediff(t.inputdate, t.preInputdate)+1 as differencesDay,
        psi.stationcodeintid,
        pa.price as meterPrice,
        t.unitpirce as accountPrice,
        t.useDay,
        sy.user_name as headPeople,
        t.days
        FROM (
        SELECT
        pac.pcid,
        pac.ammeterid,
        pac.toweraccountid,
        pac.startdate,
        pac.enddate,
        pac.prevtotalreadings,
        pac.curtotalreadings,
        pac.totalusedreadings,
        pac.inputdate,
        CASE
        WHEN datediff( pac.enddate, pac.startdate ) + 1 = 0 THEN
        0.00 ELSE ROUND( pac.totalusedreadings / ( datediff( pac.enddate, pac.startdate ) + 1 ), 2 )
        END AS useDay,
        datediff( pac.enddate, pac.startdate ) + 1 AS days,
        pac.unitpirce,
        ( SELECT startdate FROM power_account WHERE effective = 1 AND ammeterid = pac.ammeterid AND enddate <![CDATA[<]]> pac.enddate ORDER BY enddate DESC LIMIT 1 ) preStartdate,
        ( SELECT enddate FROM power_account WHERE effective = 1 AND ammeterid = pac.ammeterid AND enddate <![CDATA[<]]> pac.enddate ORDER BY enddate DESC LIMIT 1 ) preEnddate,
        ( SELECT prevtotalreadings FROM power_account WHERE effective = 1 AND ammeterid = pac.ammeterid AND enddate <![CDATA[<]]> pac.enddate ORDER BY enddate DESC LIMIT 1 ) prePrevtotalreadings,
        ( SELECT curtotalreadings FROM power_account WHERE effective = 1 AND ammeterid = pac.ammeterid AND enddate <![CDATA[<]]> pac.enddate ORDER BY enddate DESC LIMIT 1 ) preCurtotalreadings,
        ( SELECT totalusedreadings FROM power_account WHERE effective = 1 AND ammeterid = pac.ammeterid AND enddate <![CDATA[<]]> pac.enddate ORDER BY enddate DESC LIMIT 1 ) preTotalusedreadings,
        ( SELECT inputdate FROM power_account WHERE effective = 1 AND ammeterid = pac.ammeterid AND enddate <![CDATA[<]]> pac.enddate ORDER BY enddate DESC LIMIT 1 ) preInputdate
        FROM
        power_account pac
        WHERE
        pac.pcid IN
        <foreach collection="pcids" item="pcid" open="(" separator="," close=")">
            #{pcid}
        </foreach>
        AND pac.effective = 1
        UNION ALL
        SELECT
        pac.pcid,
        pac.ammeterid,
        NULL toweraccountid,
        pac.startdate,
        pac.enddate,
        pac.prevtotalreadings,
        pac.curtotalreadings,
        pac.totalusedreadings,
        pac.inputdate,
        CASE
        WHEN datediff( pac.enddate, pac.startdate ) + 1 = 0 THEN
        0.00 ELSE ROUND( pac.totalusedreadings / ( datediff( pac.enddate, pac.startdate ) + 1 ), 2 )
        END AS useDay,
        datediff( pac.enddate, pac.startdate ) + 1 AS days,
        pac.unitpirce,
        ( SELECT startdate FROM power_account_es WHERE effective = 1 AND ammeterid = pac.ammeterid AND enddate <![CDATA[<]]> pac.enddate ORDER BY enddate DESC LIMIT 1 ) preStartdate,
        ( SELECT enddate FROM power_account_es WHERE effective = 1 AND ammeterid = pac.ammeterid AND enddate <![CDATA[<]]> pac.enddate ORDER BY enddate DESC LIMIT 1 ) preEnddate,
        ( SELECT prevtotalreadings FROM power_account_es WHERE effective = 1 AND ammeterid = pac.ammeterid AND enddate <![CDATA[<]]> pac.enddate ORDER BY enddate DESC LIMIT 1 ) prePrevtotalreadings,
        ( SELECT curtotalreadings FROM power_account_es WHERE effective = 1 AND ammeterid = pac.ammeterid AND enddate <![CDATA[<]]> pac.enddate ORDER BY enddate DESC LIMIT 1 ) preCurtotalreadings,
        ( SELECT totalusedreadings FROM power_account_es WHERE effective = 1 AND ammeterid = pac.ammeterid AND enddate <![CDATA[<]]> pac.enddate ORDER BY enddate DESC LIMIT 1 ) preTotalusedreadings,
        ( SELECT inputdate FROM power_account_es WHERE effective = 1 AND ammeterid = pac.ammeterid AND enddate <![CDATA[<]]> pac.enddate ORDER BY enddate DESC LIMIT 1 ) preInputdate
        FROM
        power_account_es pac
        WHERE
        pac.pcid IN
        <foreach collection="pcids" item="pcid" open="(" separator="," close=")">
            #{pcid}
        </foreach>
        AND pac.effective = 1
        ) t
        left join ecm.toweraccount ta on t.toweraccountid=ta.id
        left join power_ammeterorprotocol pa on pa.id = t.ammeterid
        LEFT JOIN power_station_info psi on pa.stationcode = psi.id
        LEFT JOIN rmp.sys_user sy on pa.creator_id = sy.id
    </select>
    <select id="getAuditYmmc" resultType="com.sccl.modules.business.poweraudit.entity.PowerAuditVO">
        select
            pa.pcid,
            pa.toweraccountid,
            (case
            when
            (select count(1) from power_account where ammeterid = pam.id and effective = 1) = 0
            or (select count(1)
            from power_account
            where ammeterid = pam.id
            and effective = 1
            and pcid = pa.pcid
            and (select count(1) from power_account where ammeterid = pam.id and effective = 1) =
            1) = 1
            then 1
            else 0 end) isnew,
            pam.property,
            pam.electrotype
        from power_account pa
            left join power_ammeterorprotocol pam on pa.ammeterid = pam.id
        where pa.pcid = #{pcid}
    </select>
    <select id="getAuditEsYmmc" resultType="com.sccl.modules.business.poweraudit.entity.PowerAuditVO">
        select
            pae.pcid,
            pae.accountestype isnew,
            pam.property,
            pam.electrotype
        from power_account_es pae
            left join power_ammeterorprotocol pam on pae.ammeterid = pam.id
        where pae.pcid = #{pcid}
    </select>
    <select id="getPeriodAccount" resultType="com.sccl.modules.business.poweraudit.entity.DetailsDTO">

    </select>
    <select id="getQuaJt5grList" resultType="com.sccl.modules.business.account.domain.AccountQua">
        SELECT
            t.finanperiod,
            t.quantity,
            t.startdate,
            t.enddate,
            t.stationcodeintid,
            CASE
                WHEN SUBSTRING(t.finanperiod, 5, 1) = '0' THEN
                    SUBSTRING(t.finanperiod, 6, 1)
                ELSE SUBSTRING(t.finanperiod, 5, 2)
                END AS dateMonth,
            AVG(
                    t.quantity / (DATEDIFF(t.enddate, t.startdate) + 1)
            ) AS ave
        FROM
            (
                SELECT
                    finanperiod,
                    stationcodeintid,
                    quantity,
                    startdate,
                    enddate
                FROM
                    `power_station_qua_jt5gr`
                WHERE
                    stationcodeintid in
                    <foreach collection="ids" item="stationcodeintid" open="(" separator="," close=")">
                        #{stationcodeintid}
                    </foreach>
            ) t
        GROUP BY
            t.stationcodeintid, t.enddate DESC;
    </select>

</mapper>
