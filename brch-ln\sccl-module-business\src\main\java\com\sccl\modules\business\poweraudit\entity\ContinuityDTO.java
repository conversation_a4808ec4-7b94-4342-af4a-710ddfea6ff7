package com.sccl.modules.business.poweraudit.entity;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: 芮永恒
 * @CreateTime: 2024-03-04  11:51
 * @Description: 电表度数连续性
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContinuityDTO {

    /**
     * 地市
     */
    @Excel(name = "所属部门")
    private String city;

    /**
     * 区县
     */
//    @Excel(name = "区县公司")
    private String countyCompanies;

    /**
     * 运营分局
     */
    @Excel(name = "运营分局")
    private String operationsBranch;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private String degreeErrorType = "电表度数连续性";

    /**
     * 类型
     */
//    @Excel(name = "类型")
    private String errorType = "电表度数连续性";

    /**
     * 电表协议编号
     */
    @Excel(name = "电表户名/协议号码")
    private String ammeterid;

    /**
     * 台账期号
     */
    @Excel(name = "台账期号")
    private String accountNo;


    /**
     * 局站编码
     */
    @Excel(name = "集团站址编码")
    private String stationcode;

    /**
     * 铁塔站址编码
     */
    @Excel(name = "铁塔站址编码")
    private String towerSiteCode;


    /**
     * 上次起始度数
     */
    @Excel(name = "上次起始度数")
    private String lastStartDegrees;


    /**
     * 上次截止度数
     */
    @Excel(name = "上次截止度数")
    private String lastStopDegrees;

    /**
     * 本次起始度数
     */
    @Excel(name = "本次起始度数")
    private String startDegrees;

    /**
     * 本次截止度数
     */
    @Excel(name = "本次截止度数")
    private String stopDegrees;

    /**
     * 本次台账录入时间
     */
    private String auditTimeNow;

    /**
     *  台账最后编辑时间
     */
    @Excel(name = "本次台账录入时间")
    private String lasteditdate;
}
