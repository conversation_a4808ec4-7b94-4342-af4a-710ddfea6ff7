package com.sccl.modules.business.poweraudit.entity;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: 芮永恒
 * @CreateTime: 2024-03-01  16:06
 * @Description: 台账周期连续性
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PeriodicDTO {

    /**
     * 地市
     */
    @Excel(name = "所属部门")
    private String city;

    /**
     * 区县
     */
//    @Excel(name = "区县公司")
    private String countyCompanies;

    /**
     * 运营分局
     */
    @Excel(name = "运营分局")
    private String operationsBranch;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private String periodErrorType = "台账周期连续性";

    /**
     * 类型
     */
//    @Excel(name = "类型")
    private String errorType = "台账周期连续性";
    /**
     * 电表协议编号
     */
    @Excel(name = "电表户名/协议号码")
    private String ammeterid;


    /**
     * 台账期号
     */
    @Excel(name = "台账期号")
    private String accountNo;


    /**
     * 局站编码
     */
    @Excel(name = "集团站址编码")
    private String stationcode;

    /**
     * 铁塔站址编码
     */
    @Excel(name = "铁塔站址编码")
    private String towerSiteCode;

    /**
     * 上次起始时间
     */
    @Excel(name = "上次起始时间")
    private String lastStartTime;

    /**
     * 上次截止时间
     */
    @Excel(name = "上次截止时间")
    private String lastStopTime;

    /**
     * 本次起始时间
     */
    @Excel(name = "本次起始时间")
    private String startTime;

    /**
     * 本次截止时间
     */
    @Excel(name = "本次截止时间")
    private String stopTime;

    /**
     * 本次台账录入时间
     */
    private String auditTimeNow;

    /**
     *  台账最后编辑时间
     */
    @Excel(name = "本次台账录入时间")
    private String lasteditdate;
}
